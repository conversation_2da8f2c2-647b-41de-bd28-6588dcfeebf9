# Script de test simple pour le système d'inventaire NafaPlace

Write-Host "=== TEST SYSTÈME D'INVENTAIRE NAFAPLACE ===" -ForegroundColor Green
Write-Host ""

# Configuration
$inventoryApiUrl = "http://localhost:5244"
$catalogApiUrl = "http://localhost:5243"
$adminPortalUrl = "http://localhost:8081"
$sellerPortalUrl = "http://localhost:8082"

# Headers
$headers = @{
    "Content-Type" = "application/json"
    "Accept" = "application/json"
}

# Fonction de test simple
function Test-Endpoint {
    param([string]$Url, [string]$Name)
    
    Write-Host "Testing: $Name" -ForegroundColor Yellow
    try {
        $response = Invoke-RestMethod -Uri $Url -Headers $headers -TimeoutSec 10
        Write-Host "✅ SUCCESS" -ForegroundColor Green
        return @{ Success = $true; Data = $response }
    }
    catch {
        Write-Host "❌ FAILED: $($_.Exception.Message)" -ForegroundColor Red
        return @{ Success = $false }
    }
}

function Test-WebPage {
    param([string]$Url, [string]$Name)
    
    Write-Host "Testing: $Name" -ForegroundColor Yellow
    try {
        $response = Invoke-WebRequest -Uri $Url -TimeoutSec 10 -UseBasicParsing
        Write-Host "✅ SUCCESS - HTTP $($response.StatusCode)" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "❌ FAILED: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

Write-Host "🔧 PHASE 1: TESTS DES SERVICES BACKEND" -ForegroundColor Magenta
Write-Host "=" * 50

# Test 1: Health Check
$test1 = Test-Endpoint -Url "$inventoryApiUrl/health" -Name "Inventory Health Check"

# Test 2: Dashboard
$test2 = Test-Endpoint -Url "$inventoryApiUrl/api/v1/inventory/dashboard" -Name "Inventory Dashboard"

# Test 3: Catalog Integration
$test3 = Test-Endpoint -Url "$catalogApiUrl/api/v1/products" -Name "Catalog Products"

Write-Host ""
Write-Host "🖥️ PHASE 2: TESTS DES PORTAILS WEB" -ForegroundColor Magenta
Write-Host "=" * 50

# Test 4: Admin Portal
$test4 = Test-WebPage -Url $adminPortalUrl -Name "Admin Portal Access"

# Test 5: Seller Portal
$test5 = Test-WebPage -Url $sellerPortalUrl -Name "Seller Portal Access"

Write-Host ""
Write-Host "📊 PHASE 3: ANALYSE DES DONNÉES" -ForegroundColor Magenta
Write-Host "=" * 50

if ($test2.Success) {
    $dashData = $test2.Data
    Write-Host "STATISTIQUES D'INVENTAIRE:" -ForegroundColor Cyan
    Write-Host "  - Produits total: $($dashData.totalProducts)" -ForegroundColor White
    Write-Host "  - Stock faible: $($dashData.lowStockCount)" -ForegroundColor Yellow
    Write-Host "  - Ruptures: $($dashData.outOfStockProducts)" -ForegroundColor Red
    Write-Host "  - Alertes actives: $($dashData.pendingAlerts)" -ForegroundColor Orange
    Write-Host "  - Valeur inventaire: $($dashData.totalInventoryValue) $($dashData.currency)" -ForegroundColor Green
    
    if ($dashData.recentAlerts -and $dashData.recentAlerts.Count -gt 0) {
        Write-Host ""
        Write-Host "ALERTES RÉCENTES:" -ForegroundColor Red
        foreach ($alert in $dashData.recentAlerts) {
            Write-Host "  - $($alert.message)" -ForegroundColor Yellow
        }
    }
    
    if ($dashData.lowStockProducts -and $dashData.lowStockProducts.Count -gt 0) {
        Write-Host ""
        Write-Host "PRODUITS EN STOCK FAIBLE:" -ForegroundColor Orange
        foreach ($product in $dashData.lowStockProducts) {
            Write-Host "  - $($product.productName): $($product.currentStock) unités" -ForegroundColor Yellow
        }
    }
}

Write-Host ""
Write-Host "📋 RÉSUMÉ DES TESTS" -ForegroundColor Green
Write-Host "=" * 50

$totalTests = 5
$successCount = 0
if ($test1.Success) { $successCount++ }
if ($test2.Success) { $successCount++ }
if ($test3.Success) { $successCount++ }
if ($test4) { $successCount++ }
if ($test5) { $successCount++ }

Write-Host "Tests réussis: $successCount/$totalTests" -ForegroundColor $(if ($successCount -eq $totalTests) { "Green" } elseif ($successCount -gt 3) { "Yellow" } else { "Red" })

if ($successCount -eq $totalTests) {
    Write-Host "🎉 TOUS LES TESTS SONT PASSÉS!" -ForegroundColor Green
    Write-Host "Le système d'inventaire est PLEINEMENT OPÉRATIONNEL" -ForegroundColor Green
} elseif ($successCount -gt 3) {
    Write-Host "⚠️ LA PLUPART DES TESTS SONT RÉUSSIS" -ForegroundColor Yellow
    Write-Host "Le système d'inventaire est LARGEMENT FONCTIONNEL" -ForegroundColor Yellow
} else {
    Write-Host "❌ PLUSIEURS TESTS ONT ÉCHOUÉ" -ForegroundColor Red
}

Write-Host ""
Write-Host "🔗 LIENS POUR TESTS MANUELS:" -ForegroundColor Cyan
Write-Host "Admin Portal: $adminPortalUrl" -ForegroundColor White
Write-Host "Seller Portal: $sellerPortalUrl" -ForegroundColor White
Write-Host "Inventory API Swagger: $inventoryApiUrl/swagger" -ForegroundColor White

Write-Host ""
Write-Host "Test terminé!" -ForegroundColor Green
