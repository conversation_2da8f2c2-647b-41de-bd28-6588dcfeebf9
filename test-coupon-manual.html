<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Système de Coupons NafaPlace</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        button { padding: 10px 15px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
        .coupon-item { background: #f8f9fa; margin: 10px 0; padding: 10px; border-radius: 4px; border-left: 4px solid #007bff; }
        .status-active { color: #28a745; font-weight: bold; }
        .status-inactive { color: #dc3545; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎟️ Test Système de Coupons NafaPlace</h1>
        
        <div class="test-section info">
            <h3>📋 Instructions de Test</h3>
            <p>Ce test vérifie le fonctionnement complet du système de coupons :</p>
            <ol>
                <li><strong>API Backend</strong> - Test des endpoints du service Coupon</li>
                <li><strong>Validation</strong> - Test de validation d'un coupon avec un panier</li>
                <li><strong>Intégration</strong> - Test d'application d'un coupon</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>🔧 Test 1: Service Coupon API</h3>
            <button class="btn-primary" onclick="testCouponAPI()">Tester API Coupons</button>
            <button class="btn-success" onclick="testActiveCoupons()">Coupons Actifs</button>
            <button class="btn-warning" onclick="testCouponStats()">Statistiques</button>
            <div id="coupon-api-result"></div>
        </div>

        <div class="test-section">
            <h3>🎯 Test 2: Validation de Coupon</h3>
            <p>Test avec un panier simulé (75,000 GNF)</p>
            <button class="btn-primary" onclick="testCouponValidation()">Valider Coupon WELCOME10</button>
            <div id="validation-result"></div>
        </div>

        <div class="test-section">
            <h3>🛒 Test 3: Application de Coupon</h3>
            <p>Test d'application d'un coupon à un panier</p>
            <button class="btn-success" onclick="testCouponApplication()">Appliquer Coupon</button>
            <div id="application-result"></div>
        </div>

        <div class="test-section">
            <h3>📊 Résultats des Tests</h3>
            <div id="test-summary"></div>
        </div>
    </div>

    <script>
        const COUPON_API_URL = 'http://localhost:5009';
        const CART_API_URL = 'http://localhost:5003';
        
        let testResults = {
            couponAPI: false,
            validation: false,
            application: false
        };

        async function testCouponAPI() {
            const resultDiv = document.getElementById('coupon-api-result');
            resultDiv.innerHTML = '<p>🔄 Test en cours...</p>';
            
            try {
                const response = await fetch(`${COUPON_API_URL}/api/coupon`);
                const coupons = await response.json();
                
                if (response.ok) {
                    testResults.couponAPI = true;
                    let html = '<div class="success"><h4>✅ API Coupons fonctionne!</h4>';
                    html += `<p><strong>Nombre de coupons:</strong> ${coupons.length}</p>`;
                    
                    if (coupons.length > 0) {
                        html += '<h5>Coupons disponibles:</h5>';
                        coupons.forEach(coupon => {
                            const status = coupon.isActive ? 'ACTIF' : 'INACTIF';
                            const statusClass = coupon.isActive ? 'status-active' : 'status-inactive';
                            html += `<div class="coupon-item">
                                <strong>${coupon.code}</strong> - ${coupon.name}<br>
                                <span class="${statusClass}">${status}</span> | 
                                Type: ${getCouponTypeName(coupon.type)} | 
                                Valeur: ${coupon.value}${coupon.type === 2 ? '%' : ' GNF'}<br>
                                <small>${coupon.description}</small>
                            </div>`;
                        });
                    }
                    html += '</div>';
                    resultDiv.innerHTML = html;
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><h4>❌ Erreur API Coupons</h4><p>${error.message}</p></div>`;
            }
            updateTestSummary();
        }

        async function testActiveCoupons() {
            const resultDiv = document.getElementById('coupon-api-result');
            
            try {
                const response = await fetch(`${COUPON_API_URL}/api/coupon/active`);
                const activeCoupons = await response.json();
                
                if (response.ok) {
                    let html = '<div class="success"><h4>✅ Coupons Actifs</h4>';
                    html += `<p><strong>Nombre de coupons actifs:</strong> ${activeCoupons.length}</p></div>`;
                    resultDiv.innerHTML = html;
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><h4>❌ Erreur Coupons Actifs</h4><p>${error.message}</p></div>`;
            }
        }

        async function testCouponStats() {
            const resultDiv = document.getElementById('coupon-api-result');
            
            try {
                const response = await fetch(`${COUPON_API_URL}/api/coupon/stats`);
                const stats = await response.json();
                
                if (response.ok) {
                    let html = '<div class="success"><h4>✅ Statistiques des Coupons</h4>';
                    html += `<p><strong>Total:</strong> ${stats.totalCoupons} | `;
                    html += `<strong>Actifs:</strong> ${stats.activeCoupons} | `;
                    html += `<strong>Expirés:</strong> ${stats.expiredCoupons} | `;
                    html += `<strong>Utilisations:</strong> ${stats.totalUsages}</p></div>`;
                    resultDiv.innerHTML = html;
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><h4>❌ Erreur Statistiques</h4><p>${error.message}</p></div>`;
            }
        }

        async function testCouponValidation() {
            const resultDiv = document.getElementById('validation-result');
            resultDiv.innerHTML = '<p>🔄 Validation en cours...</p>';
            
            const validationData = {
                couponCode: "WELCOME10",
                cart: {
                    SubTotal: 75000,
                    Items: [{
                        ProductId: 1,
                        Quantity: 2,
                        UnitPrice: 37500,
                        CategoryId: 1,
                        SellerId: 1
                    }],
                    UserId: "test-user"
                }
            };
            
            try {
                const response = await fetch(`${COUPON_API_URL}/api/coupon/validate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(validationData)
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    testResults.validation = result.success;
                    let html = `<div class="${result.success ? 'success' : 'error'}">`;
                    html += `<h4>${result.success ? '✅' : '❌'} Validation du coupon</h4>`;
                    html += `<p><strong>Coupon valide:</strong> ${result.success ? 'OUI' : 'NON'}</p>`;
                    if (result.success) {
                        html += `<p><strong>Réduction:</strong> ${result.discountAmount} GNF</p>`;
                        html += `<p><strong>Nouveau total:</strong> ${result.newTotal} GNF</p>`;
                    }
                    html += `<p><strong>Message:</strong> ${result.message}</p>`;
                    html += '</div>';
                    resultDiv.innerHTML = html;
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><h4>❌ Erreur Validation</h4><p>${error.message}</p></div>`;
            }
            updateTestSummary();
        }

        async function testCouponApplication() {
            const resultDiv = document.getElementById('application-result');
            resultDiv.innerHTML = '<p>🔄 Application en cours...</p>';
            
            try {
                // Simuler l'application d'un coupon via l'API Cart
                const response = await fetch(`${CART_API_URL}/api/cart/test-user/apply-coupon`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ couponCode: "WELCOME10" })
                });
                
                if (response.ok) {
                    const result = await response.json();
                    testResults.application = true;
                    let html = '<div class="success"><h4>✅ Application du coupon réussie</h4>';
                    html += `<p><strong>Coupon appliqué:</strong> ${result.couponCode}</p>`;
                    html += `<p><strong>Réduction:</strong> ${result.couponDiscount} GNF</p>`;
                    html += `<p><strong>Nouveau total:</strong> ${result.total} GNF</p></div>`;
                    resultDiv.innerHTML = html;
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><h4>❌ Erreur Application</h4><p>${error.message}</p><p><em>Note: Cette fonctionnalité nécessite un panier existant</em></p></div>`;
            }
            updateTestSummary();
        }

        function getCouponTypeName(type) {
            switch(type) {
                case 1: return 'Montant fixe';
                case 2: return 'Pourcentage';
                case 3: return 'Livraison gratuite';
                case 4: return 'Buy X Get Y';
                default: return 'Autre';
            }
        }

        function updateTestSummary() {
            const summaryDiv = document.getElementById('test-summary');
            const totalTests = Object.keys(testResults).length;
            const passedTests = Object.values(testResults).filter(result => result).length;
            
            let html = '<h4>📊 Résumé</h4>';
            html += `<p><strong>Tests réussis:</strong> ${passedTests}/${totalTests}</p>`;
            
            if (passedTests === totalTests) {
                html += '<div class="success">🎉 Tous les tests sont passés! Le système de coupons fonctionne parfaitement.</div>';
            } else if (passedTests > 0) {
                html += '<div class="info">⚠️ Certains tests sont réussis. Le système fonctionne partiellement.</div>';
            } else {
                html += '<div class="error">❌ Aucun test n\'est passé. Vérifiez la configuration des services.</div>';
            }
            
            summaryDiv.innerHTML = html;
        }

        // Test automatique au chargement
        window.onload = function() {
            setTimeout(() => {
                testCouponAPI();
            }, 1000);
        };
    </script>
</body>
</html>
