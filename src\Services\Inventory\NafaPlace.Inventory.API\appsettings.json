{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Host=localhost;Port=5438;Database=NafaPlace.Inventory;Username=postgres;Password=*****************"}, "JwtSettings": {"Secret": "NafaPlaceSecretKey2025ForProductionEnvironment", "Issuer": "NafaPlace", "Audience": "NafaPlaceApi", "ExpiryInMinutes": 60}, "ApiSettings": {"NotificationApiUrl": "http://localhost:5008", "CatalogApiUrl": "http://localhost:5243"}}