# Script de test pour le système d'inventaire NafaPlace
# Teste les fonctionnalités principales du service Inventory

Write-Host "=== Test du Système d'Inventaire NafaPlace ===" -ForegroundColor Green
Write-Host ""

# Configuration
$inventoryApiUrl = "http://localhost:5244"
$catalogApiUrl = "http://localhost:5243"
$notificationApiUrl = "http://localhost:5007"

# Headers pour les requêtes
$headers = @{
    "Content-Type" = "application/json"
    "Accept" = "application/json"
}

# Fonction pour tester une API
function Test-ApiEndpoint {
    param(
        [string]$Url,
        [string]$Description,
        [string]$Method = "GET",
        [object]$Body = $null
    )
    
    Write-Host "Testing: $Description" -ForegroundColor Yellow
    Write-Host "URL: $Url" -ForegroundColor Gray
    
    try {
        if ($Method -eq "GET") {
            $response = Invoke-RestMethod -Uri $Url -Method $Method -Headers $headers -TimeoutSec 10
        } else {
            $jsonBody = $Body | ConvertTo-Json -Depth 10
            $response = Invoke-RestMethod -Uri $Url -Method $Method -Headers $headers -Body $jsonBody -TimeoutSec 10
        }
        
        Write-Host "✅ SUCCESS" -ForegroundColor Green
        Write-Host "Response: $($response | ConvertTo-Json -Depth 2)" -ForegroundColor Cyan
        Write-Host ""
        return $true
    }
    catch {
        Write-Host "❌ FAILED: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host ""
        return $false
    }
}

# Tests du service Inventory
Write-Host "1. Test de connectivité du service Inventory" -ForegroundColor Magenta
$inventoryHealth = Test-ApiEndpoint -Url "$inventoryApiUrl/health" -Description "Health Check Inventory"

Write-Host "2. Test du dashboard d'inventaire" -ForegroundColor Magenta
$inventoryDashboard = Test-ApiEndpoint -Url "$inventoryApiUrl/api/v1/inventory/dashboard" -Description "Inventory Dashboard"

Write-Host "3. Test des alertes de stock" -ForegroundColor Magenta
$stockAlerts = Test-ApiEndpoint -Url "$inventoryApiUrl/api/v1/inventory/alerts" -Description "Stock Alerts"

Write-Host "4. Test des réservations actives" -ForegroundColor Magenta
$activeReservations = Test-ApiEndpoint -Url "$inventoryApiUrl/api/v1/inventory/reservations" -Description "Active Reservations"

Write-Host "5. Test des mouvements de stock" -ForegroundColor Magenta
$stockMovements = Test-ApiEndpoint -Url "$inventoryApiUrl/api/v1/inventory/movements" -Description "Stock Movements"

# Tests d'intégration avec le service Catalog
Write-Host "6. Test d'intégration avec le service Catalog" -ForegroundColor Magenta
$catalogProducts = Test-ApiEndpoint -Url "$catalogApiUrl/api/v1/products" -Description "Catalog Products"

# Test de création d'une alerte de stock (nécessite authentification)
Write-Host "7. Test de création d'alerte de stock" -ForegroundColor Magenta
$alertData = @{
    productId = 1
    currentStock = 5
    threshold = 10
}
# Note: Ce test nécessiterait une authentification, donc on le simule
Write-Host "⚠️  Test de création d'alerte nécessite une authentification" -ForegroundColor Yellow
Write-Host ""

# Tests des tâches de maintenance
Write-Host "8. Test du statut des tâches de maintenance" -ForegroundColor Magenta
$maintenanceStatus = Test-ApiEndpoint -Url "$inventoryApiUrl/api/v1/inventory/maintenance/status" -Description "Maintenance Status"

# Résumé des tests
Write-Host "=== RÉSUMÉ DES TESTS ===" -ForegroundColor Green
$totalTests = 7
$successfulTests = 0

if ($inventoryHealth) { $successfulTests++ }
if ($inventoryDashboard) { $successfulTests++ }
if ($stockAlerts) { $successfulTests++ }
if ($activeReservations) { $successfulTests++ }
if ($stockMovements) { $successfulTests++ }
if ($catalogProducts) { $successfulTests++ }
if ($maintenanceStatus) { $successfulTests++ }

Write-Host "Tests réussis: $successfulTests/$totalTests" -ForegroundColor $(if ($successfulTests -eq $totalTests) { "Green" } else { "Yellow" })

if ($successfulTests -eq $totalTests) {
    Write-Host "🎉 Tous les tests sont passés avec succès!" -ForegroundColor Green
} elseif ($successfulTests -gt 0) {
    Write-Host "⚠️  Certains tests ont échoué. Vérifiez les services." -ForegroundColor Yellow
} else {
    Write-Host "❌ Tous les tests ont échoué. Vérifiez que les services sont démarrés." -ForegroundColor Red
}

Write-Host ""
Write-Host "=== VÉRIFICATIONS SUPPLÉMENTAIRES ===" -ForegroundColor Green

# Vérifier les conteneurs Docker
Write-Host "Vérification des conteneurs Docker..." -ForegroundColor Yellow
try {
    $containers = docker ps --filter "name=nafaplace" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    Write-Host $containers -ForegroundColor Cyan
}
catch {
    Write-Host "❌ Erreur lors de la vérification des conteneurs Docker" -ForegroundColor Red
}

Write-Host ""
Write-Host "Test terminé!" -ForegroundColor Green
