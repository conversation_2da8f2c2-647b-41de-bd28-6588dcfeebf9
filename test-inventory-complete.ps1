# Script de test complet pour le système d'inventaire NafaPlace
# Tests côté Admin et Vendeur avec conditions réelles

Write-Host "=== TEST COMPLET DU SYSTÈME D'INVENTAIRE NAFAPLACE ===" -ForegroundColor Green
Write-Host ""

# Configuration
$inventoryApiUrl = "http://localhost:5244"
$catalogApiUrl = "http://localhost:5243"
$notificationApiUrl = "http://localhost:5007"
$adminPortalUrl = "http://localhost:8081"
$sellerPortalUrl = "http://localhost:8082"

# Headers pour les requêtes
$headers = @{
    "Content-Type" = "application/json"
    "Accept" = "application/json"
}

# Fonction pour tester une API
function Test-ApiEndpoint {
    param(
        [string]$Url,
        [string]$Description,
        [string]$Method = "GET",
        [object]$Body = $null,
        [hashtable]$Headers = $headers
    )
    
    Write-Host "Testing: $Description" -ForegroundColor Yellow
    Write-Host "URL: $Url" -ForegroundColor Gray
    
    try {
        if ($Method -eq "GET") {
            $response = Invoke-RestMethod -Uri $Url -Method $Method -Headers $Headers -TimeoutSec 10
        } else {
            $jsonBody = $Body | ConvertTo-Json -Depth 10
            $response = Invoke-RestMethod -Uri $Url -Method $Method -Headers $Headers -Body $jsonBody -TimeoutSec 10
        }
        
        Write-Host "✅ SUCCESS" -ForegroundColor Green
        if ($response -is [string] -and $response.Length -gt 200) {
            Write-Host "Response: [Large response - truncated]" -ForegroundColor Cyan
        } else {
            Write-Host "Response: $($response | ConvertTo-Json -Depth 2)" -ForegroundColor Cyan
        }
        Write-Host ""
        return @{ Success = $true; Data = $response }
    }
    catch {
        Write-Host "❌ FAILED: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host ""
        return @{ Success = $false; Error = $_.Exception.Message }
    }
}

# Fonction pour tester l'accès aux portails web
function Test-WebPortal {
    param(
        [string]$Url,
        [string]$PortalName
    )
    
    Write-Host "Testing: $PortalName Portal Access" -ForegroundColor Yellow
    Write-Host "URL: $Url" -ForegroundColor Gray
    
    try {
        $response = Invoke-WebRequest -Uri $Url -TimeoutSec 10 -UseBasicParsing
        Write-Host "✅ SUCCESS - HTTP $($response.StatusCode)" -ForegroundColor Green
        Write-Host "Content Length: $($response.Content.Length) bytes" -ForegroundColor Cyan
        Write-Host ""
        return $true
    }
    catch {
        Write-Host "❌ FAILED: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host ""
        return $false
    }
}

# =============================================================================
# PHASE 1: TESTS DES SERVICES BACKEND
# =============================================================================

Write-Host "🔧 PHASE 1: TESTS DES SERVICES BACKEND" -ForegroundColor Magenta
Write-Host "=" * 50

# Test 1: Health Check du service Inventory
$healthCheck = Test-ApiEndpoint -Url "$inventoryApiUrl/health" -Description "Inventory Service Health Check"

# Test 2: Dashboard d'inventaire (données globales)
$dashboard = Test-ApiEndpoint -Url "$inventoryApiUrl/api/v1/inventory/dashboard" -Description "Inventory Dashboard - Global Stats"

# Test 3: Intégration avec le service Catalog
$catalogProducts = Test-ApiEndpoint -Url "$catalogApiUrl/api/v1/products" -Description "Catalog Integration - Products List"

# Test 4: Service de notifications
$notificationHealth = Test-ApiEndpoint -Url "$notificationApiUrl/health" -Description "Notification Service Health Check"

# =============================================================================
# PHASE 2: TESTS DES PORTAILS WEB
# =============================================================================

Write-Host "🖥️ PHASE 2: TESTS DES PORTAILS WEB" -ForegroundColor Magenta
Write-Host "=" * 50

# Test 5: Accès Admin Portal
$adminPortalAccess = Test-WebPortal -Url $adminPortalUrl -PortalName "Admin"

# Test 6: Accès Seller Portal
$sellerPortalAccess = Test-WebPortal -Url $sellerPortalUrl -PortalName "Seller"

# =============================================================================
# PHASE 3: TESTS FONCTIONNELS AVANCÉS
# =============================================================================

Write-Host "⚙️ PHASE 3: TESTS FONCTIONNELS AVANCÉS" -ForegroundColor Magenta
Write-Host "=" * 50

# Test 7: Statistiques détaillées d'inventaire
if ($dashboard.Success) {
    Write-Host "📊 ANALYSE DU DASHBOARD:" -ForegroundColor Cyan
    $dashData = $dashboard.Data
    Write-Host "  - Produits total: $($dashData.totalProducts)" -ForegroundColor White
    Write-Host "  - Stock faible: $($dashData.lowStockCount)" -ForegroundColor Yellow
    Write-Host "  - Ruptures: $($dashData.outOfStockProducts)" -ForegroundColor Red
    Write-Host "  - Alertes actives: $($dashData.pendingAlerts)" -ForegroundColor Orange
    Write-Host "  - Valeur inventaire: $($dashData.totalInventoryValue) $($dashData.currency)" -ForegroundColor Green
    Write-Host ""
}

# Test 8: Vérification des alertes récentes
if ($dashboard.Success -and $dashboard.Data.recentAlerts) {
    Write-Host "🚨 ALERTES RÉCENTES:" -ForegroundColor Red
    foreach ($alert in $dashboard.Data.recentAlerts) {
        $alertType = switch ($alert.type) {
            1 { "Stock Faible" }
            2 { "Rupture" }
            default { "Autre" }
        }
        Write-Host "  - [$alertType] $($alert.message)" -ForegroundColor Yellow
        Write-Host "    Produit ID: $($alert.productId), Stock: $($alert.currentStock)" -ForegroundColor Gray
    }
    Write-Host ""
}

# Test 9: Vérification des produits en stock faible
if ($dashboard.Success -and $dashboard.Data.lowStockProducts) {
    Write-Host "📦 PRODUITS EN STOCK FAIBLE:" -ForegroundColor Orange
    foreach ($product in $dashboard.Data.lowStockProducts) {
        Write-Host "  - $($product.productName): $($product.currentStock) unités" -ForegroundColor Yellow
        Write-Host "    Revenus: $($product.revenue) $($product.currency)" -ForegroundColor Green
    }
    Write-Host ""
}
}

# =============================================================================
# PHASE 4: TESTS DE MAINTENANCE
# =============================================================================

Write-Host "🔧 PHASE 4: TESTS DE MAINTENANCE" -ForegroundColor Magenta
Write-Host "=" * 50

# Test 10: Statut des tâches de maintenance
$maintenanceStatus = Test-ApiEndpoint -Url "$inventoryApiUrl/api/v1/inventory/maintenance/status" -Description "Maintenance Tasks Status"

# =============================================================================
# RÉSUMÉ DES TESTS
# =============================================================================

Write-Host "📋 RÉSUMÉ DES TESTS" -ForegroundColor Green
Write-Host "=" * 50

$totalTests = 10
$successfulTests = 0

# Comptage des succès
if ($healthCheck.Success) { $successfulTests++ }
if ($dashboard.Success) { $successfulTests++ }
if ($catalogProducts.Success) { $successfulTests++ }
if ($notificationHealth.Success) { $successfulTests++ }
if ($adminPortalAccess) { $successfulTests++ }
if ($sellerPortalAccess) { $successfulTests++ }
if ($maintenanceStatus.Success) { $successfulTests++ }

# Tests fonctionnels (comptés comme 3 tests)
if ($dashboard.Success) { $successfulTests += 3 }

Write-Host "Tests réussis: $successfulTests/$totalTests" -ForegroundColor $(if ($successfulTests -eq $totalTests) { "Green" } elseif ($successfulTests -gt 7) { "Yellow" } else { "Red" })

if ($successfulTests -eq $totalTests) {
    Write-Host "🎉 TOUS LES TESTS SONT PASSÉS AVEC SUCCÈS!" -ForegroundColor Green
    Write-Host "Le système d'inventaire est PLEINEMENT OPÉRATIONNEL" -ForegroundColor Green
} elseif ($successfulTests -gt 7) {
    Write-Host "⚠️ LA PLUPART DES TESTS SONT RÉUSSIS" -ForegroundColor Yellow
    Write-Host "Le système d'inventaire est LARGEMENT FONCTIONNEL" -ForegroundColor Yellow
} else {
    Write-Host "❌ PLUSIEURS TESTS ONT ÉCHOUÉ" -ForegroundColor Red
    Write-Host "Vérifiez les services et la configuration" -ForegroundColor Red
}

Write-Host ""
Write-Host "🔗 LIENS POUR TESTS MANUELS:" -ForegroundColor Cyan
Write-Host "Admin Portal: $adminPortalUrl" -ForegroundColor White
Write-Host "Seller Portal: $sellerPortalUrl" -ForegroundColor White
Write-Host "Inventory API: $inventoryApiUrl/swagger" -ForegroundColor White

Write-Host ""
Write-Host "Test terminé!" -ForegroundColor Green
