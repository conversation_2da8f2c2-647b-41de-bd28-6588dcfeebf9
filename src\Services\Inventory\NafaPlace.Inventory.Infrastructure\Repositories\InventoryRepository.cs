using Microsoft.EntityFrameworkCore;
using NafaPlace.Inventory.Application.DTOs;
using NafaPlace.Inventory.Application.Interfaces;
using NafaPlace.Inventory.Domain.Models;
using NafaPlace.Inventory.Domain.Enums;
using NafaPlace.Inventory.Infrastructure.Data;
using System.Text.Json;

namespace NafaPlace.Inventory.Infrastructure.Repositories;

public class InventoryRepository : IInventoryRepository
{
    private readonly InventoryDbContext _context;
    private readonly HttpClient _httpClient;

    public InventoryRepository(InventoryDbContext context, HttpClient httpClient)
    {
        _context = context;
        _httpClient = httpClient;
    }

    // Stock Reservations
    public async Task<StockReservation> CreateReservationAsync(StockReservation reservation)
    {
        _context.StockReservations.Add(reservation);
        await _context.SaveChangesAsync();
        return reservation;
    }

    public async Task<bool> UpdateReservationAsync(StockReservation reservation)
    {
        _context.StockReservations.Update(reservation);
        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<StockReservation?> GetReservationAsync(int reservationId)
    {
        return await _context.StockReservations
            .FirstOrDefaultAsync(r => r.Id == reservationId);
    }

    public async Task<List<StockReservation>> GetUserReservationsAsync(string userId)
    {
        return await _context.StockReservations
            .Where(r => r.UserId == userId)
            .OrderByDescending(r => r.ReservedAt)
            .ToListAsync();
    }

    public async Task<List<StockReservation>> GetProductReservationsAsync(int productId)
    {
        return await _context.StockReservations
            .Where(r => r.ProductId == productId && r.Status == ReservationStatus.Active)
            .OrderByDescending(r => r.ReservedAt)
            .ToListAsync();
    }

    public async Task<List<StockReservation>> GetExpiredReservationsAsync()
    {
        var now = DateTime.UtcNow;
        return await _context.StockReservations
            .Where(r => r.Status == ReservationStatus.Active && r.ExpiresAt < now)
            .ToListAsync();
    }

    public async Task<int> GetReservedStockAsync(int productId)
    {
        var now = DateTime.UtcNow;
        return await _context.StockReservations
            .Where(r => r.ProductId == productId && 
                       r.Status == ReservationStatus.Active && 
                       r.ExpiresAt > now)
            .SumAsync(r => r.Quantity);
    }

    public async Task<int> GetActiveReservationsCountAsync(int? sellerId = null)
    {
        var query = _context.StockReservations
            .Where(r => r.Status == ReservationStatus.Active);

        if (sellerId.HasValue)
        {
            // Ici on devrait joindre avec la table des produits pour filtrer par vendeur
            // Pour l'instant, on retourne le count total
        }

        return await query.CountAsync();
    }



    // Stock Alerts
    public async Task<StockAlert> CreateAlertAsync(StockAlert alert)
    {
        _context.StockAlerts.Add(alert);
        await _context.SaveChangesAsync();
        return alert;
    }

    public async Task<bool> UpdateAlertAsync(StockAlert alert)
    {
        _context.StockAlerts.Update(alert);
        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<StockAlert?> GetAlertAsync(int alertId)
    {
        return await _context.StockAlerts
            .Include(a => a.Notifications)
            .FirstOrDefaultAsync(a => a.Id == alertId);
    }



    public async Task<List<StockAlert>> GetSellerAlertsAsync(int sellerId)
    {
        return await _context.StockAlerts
            .Where(a => a.SellerId == sellerId)
            .OrderByDescending(a => a.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<StockAlert>> GetPendingAlertsAsync()
    {
        return await _context.StockAlerts
            .Where(a => a.IsActive && !a.IsAcknowledged)
            .OrderBy(a => a.Severity)
            .ThenByDescending(a => a.CreatedAt)
            .ToListAsync();
    }

    public async Task<int> GetPendingAlertsCountAsync(int? sellerId = null)
    {
        var query = _context.StockAlerts
            .Where(a => a.IsActive && !a.IsAcknowledged);

        if (sellerId.HasValue)
        {
            query = query.Where(a => a.SellerId == sellerId.Value);
        }

        return await query.CountAsync();
    }

    // Stock Movements
    public async Task<StockMovement> CreateMovementAsync(StockMovement movement)
    {
        _context.StockMovements.Add(movement);
        await _context.SaveChangesAsync();
        return movement;
    }





    public async Task<int> CleanupOldMovementsAsync(int daysToKeep = 90)
    {
        var cutoffDate = DateTime.UtcNow.AddDays(-daysToKeep);
        var oldMovements = await _context.StockMovements
            .Where(m => m.CreatedAt < cutoffDate)
            .ToListAsync();

        if (oldMovements.Any())
        {
            _context.StockMovements.RemoveRange(oldMovements);
            await _context.SaveChangesAsync();
        }

        return oldMovements.Count;
    }

    // Analytics
    public async Task<List<TopProductDto>> GetTopSellingProductsAsync(int? sellerId = null, int count = 10)
    {
        var query = _context.StockMovements
            .Where(m => m.Type == MovementType.Sale);

        if (sellerId.HasValue)
        {
            query = query.Where(m => m.SellerId == sellerId.Value);
        }

        var topProducts = await query
            .GroupBy(m => new { m.ProductId, m.ProductName })
            .Select(g => new TopProductDto
            {
                ProductId = g.Key.ProductId,
                ProductName = g.Key.ProductName,
                SoldQuantity = g.Sum(m => m.Quantity),
                Revenue = 0, // Serait calculé avec les prix
                Currency = "GNF"
            })
            .OrderByDescending(p => p.SoldQuantity)
            .Take(count)
            .ToListAsync();

        return topProducts;
    }

    public async Task<List<TopProductDto>> GetLowStockProductsAsync(int? sellerId = null, int threshold = 10)
    {
        return await GetLowStockProductsFromCatalogAsync(sellerId, threshold);
    }

    // Méthodes manquantes de l'interface
    public async Task<InventoryDashboardDto> GetInventoryDashboardAsync(int? sellerId = null)
    {
        try
        {
            // Récupérer les données depuis l'API Catalog
            var catalogData = await GetCatalogDataAsync(sellerId);

            var activeReservations = await _context.StockReservations
                .Where(r => r.Status == ReservationStatus.Active)
                .CountAsync();

            var pendingAlerts = await _context.StockAlerts
                .Where(a => a.IsActive && (sellerId == null || a.SellerId == sellerId))
                .CountAsync();

            var recentAlerts = await GetActiveAlertsAsync(sellerId);
            var recentMovements = await GetRecentMovementsAsync(sellerId);
            var topSellingProducts = await GetTopSellingProductsAsync(sellerId);
            var lowStockProducts = await GetLowStockProductsFromCatalogAsync(sellerId);

            return new InventoryDashboardDto
            {
                TotalProducts = catalogData.TotalProducts,
                LowStockCount = catalogData.LowStockCount,
                OutOfStockProducts = catalogData.OutOfStockProducts,
                ActiveReservations = activeReservations,
                PendingAlerts = pendingAlerts,
                TotalInventoryValue = catalogData.TotalInventoryValue,
                Currency = "GNF",
                RecentAlerts = recentAlerts.Take(5).ToList(),
                RecentMovements = recentMovements.Take(10).ToList(),
                TopSellingProducts = topSellingProducts,
                LowStockProducts = lowStockProducts
            };
        }
        catch (Exception ex)
        {
            // En cas d'erreur, retourner des données par défaut
            return new InventoryDashboardDto
            {
                TotalProducts = 0,
                LowStockCount = 0,
                OutOfStockProducts = 0,
                ActiveReservations = 0,
                PendingAlerts = 0,
                TotalInventoryValue = 0,
                Currency = "GNF",
                RecentAlerts = new List<StockAlertDto>(),
                RecentMovements = new List<StockMovementDto>(),
                TopSellingProducts = new List<TopProductDto>(),
                LowStockProducts = new List<TopProductDto>()
            };
        }
    }

    public async Task<List<StockAlertDto>> GetActiveAlertsAsync(int? sellerId = null)
    {
        var query = _context.StockAlerts
            .Where(a => a.IsActive);

        if (sellerId.HasValue)
        {
            query = query.Where(a => a.SellerId == sellerId.Value);
        }

        var alerts = await query
            .OrderByDescending(a => a.CreatedAt)
            .ToListAsync();

        return alerts.Select(a => new StockAlertDto
        {
            Id = a.Id,
            ProductId = a.ProductId,
            Type = a.Type,
            Message = a.Message,
            ThresholdValue = a.ThresholdValue,
            CurrentStock = a.CurrentStock,
            IsActive = a.IsActive,
            CreatedAt = a.CreatedAt,
            AcknowledgedAt = a.AcknowledgedAt,
            AcknowledgedBy = a.AcknowledgedBy
        }).ToList();
    }

    public async Task<bool> AcknowledgeAlertAsync(int alertId, string acknowledgedBy)
    {
        var alert = await _context.StockAlerts.FindAsync(alertId);
        if (alert == null) return false;

        alert.IsActive = false;
        alert.AcknowledgedAt = DateTime.UtcNow;
        alert.AcknowledgedBy = acknowledgedBy;

        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<List<StockMovementDto>> GetProductMovementsAsync(int productId, int page, int pageSize)
    {
        var movements = await _context.StockMovements
            .Where(m => m.ProductId == productId)
            .OrderByDescending(m => m.CreatedAt)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        return movements.Select(m => new StockMovementDto
        {
            Id = m.Id,
            ProductId = m.ProductId,
            Type = m.Type,
            Quantity = m.Quantity,
            Reason = m.Reason,
            UserId = m.UserId,
            CreatedAt = m.CreatedAt,
            Notes = m.Notes
        }).ToList();
    }

    public async Task<List<StockMovementDto>> GetSellerMovementsAsync(int sellerId, int page, int pageSize)
    {
        var movements = await _context.StockMovements
            .Where(m => m.UserId == sellerId.ToString())
            .OrderByDescending(m => m.CreatedAt)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        return movements.Select(m => new StockMovementDto
        {
            Id = m.Id,
            ProductId = m.ProductId,
            Type = m.Type,
            Quantity = m.Quantity,
            Reason = m.Reason,
            UserId = m.UserId,
            CreatedAt = m.CreatedAt,
            Notes = m.Notes
        }).ToList();
    }

    public async Task<List<StockReservationDto>> GetActiveReservationsAsync(int? sellerId = null)
    {
        var query = _context.StockReservations
            .Where(r => r.Status == ReservationStatus.Active);

        if (sellerId.HasValue)
        {
            // Filtrer par vendeur nécessiterait une jointure avec les produits
        }

        var reservations = await query
            .OrderByDescending(r => r.ReservedAt)
            .ToListAsync();

        return reservations.Select(r => new StockReservationDto
        {
            Id = r.Id,
            ProductId = r.ProductId,
            UserId = r.UserId,
            SessionId = r.SessionId,
            Quantity = r.Quantity,
            Status = r.Status,
            ReservedAt = r.ReservedAt,
            ExpiresAt = r.ExpiresAt,
            Reason = r.Reason
        }).ToList();
    }

    public async Task<bool> ReleaseReservationAsync(int reservationId, string reason)
    {
        var reservation = await _context.StockReservations.FindAsync(reservationId);
        if (reservation == null) return false;

        reservation.Status = ReservationStatus.Released;
        reservation.UpdatedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();
        return true;
    }

    public async Task RecalculateStockLevelsAsync()
    {
        // Logique de recalcul des niveaux de stock
        // Pour l'instant, on ne fait rien
        await Task.CompletedTask;
    }

    public async Task<int> CleanupExpiredReservationsAsync()
    {
        var expiredReservations = await _context.StockReservations
            .Where(r => r.Status == ReservationStatus.Active && r.ExpiresAt < DateTime.UtcNow)
            .ToListAsync();

        foreach (var reservation in expiredReservations)
        {
            reservation.Status = ReservationStatus.Expired;
            reservation.UpdatedAt = DateTime.UtcNow;
        }

        await _context.SaveChangesAsync();
        return expiredReservations.Count;
    }

    private async Task<List<StockMovementDto>> GetRecentMovementsAsync(int? sellerId = null)
    {
        var query = _context.StockMovements.AsQueryable();

        if (sellerId.HasValue)
        {
            query = query.Where(m => m.UserId == sellerId.ToString());
        }

        var movements = await query
            .OrderByDescending(m => m.CreatedAt)
            .Take(10)
            .ToListAsync();

        return movements.Select(m => new StockMovementDto
        {
            Id = m.Id,
            ProductId = m.ProductId,
            Type = m.Type,
            Quantity = m.Quantity,
            Reason = m.Reason,
            UserId = m.UserId,
            CreatedAt = m.CreatedAt,
            Notes = m.Notes
        }).ToList();
    }

    private async Task<CatalogInventoryData> GetCatalogDataAsync(int? sellerId = null)
    {
        try
        {
            var url = sellerId.HasValue
                ? $"api/v1/products/inventory-summary?sellerId={sellerId}"
                : "api/v1/products/inventory-summary";

            var response = await _httpClient.GetAsync(url);
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                var data = System.Text.Json.JsonSerializer.Deserialize<CatalogInventoryData>(json, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
                return data ?? new CatalogInventoryData();
            }
        }
        catch (Exception)
        {
            // En cas d'erreur, retourner des données par défaut
        }

        return new CatalogInventoryData();
    }

    private async Task<List<TopProductDto>> GetLowStockProductsFromCatalogAsync(int? sellerId = null, int threshold = 10)
    {
        try
        {
            var url = sellerId.HasValue
                ? $"api/v1/products/low-stock?sellerId={sellerId}&threshold={threshold}"
                : $"api/v1/products/low-stock?threshold={threshold}";

            var response = await _httpClient.GetAsync(url);
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                var products = System.Text.Json.JsonSerializer.Deserialize<List<TopProductDto>>(json, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
                return products ?? new List<TopProductDto>();
            }
        }
        catch (Exception)
        {
            // En cas d'erreur, retourner une liste vide
        }

        return new List<TopProductDto>();
    }

    public async Task<bool> CreateLowStockAlertAsync(int productId, string productName, int currentStock, int threshold, int sellerId, string? sellerName = null)
    {
        try
        {
            var alert = new StockAlert
            {
                ProductId = productId,
                ProductName = productName,
                Type = AlertType.LowStock,
                Severity = currentStock <= 5 ? AlertSeverity.Critical : AlertSeverity.Warning,
                Message = $"Stock faible pour {productName}. Stock actuel: {currentStock}, Seuil: {threshold}",
                CurrentStock = currentStock,
                ThresholdValue = threshold,
                SellerId = sellerId,
                SellerName = sellerName,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _context.StockAlerts.Add(alert);
            await _context.SaveChangesAsync();
            return true;
        }
        catch (Exception)
        {
            return false;
        }
    }

    public async Task<bool> CreateOutOfStockAlertAsync(int productId, string productName, int sellerId, string? sellerName = null)
    {
        try
        {
            var alert = new StockAlert
            {
                ProductId = productId,
                ProductName = productName,
                Type = AlertType.OutOfStock,
                Severity = AlertSeverity.Emergency,
                Message = $"Rupture de stock pour {productName}",
                CurrentStock = 0,
                SellerId = sellerId,
                SellerName = sellerName,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _context.StockAlerts.Add(alert);
            await _context.SaveChangesAsync();
            return true;
        }
        catch (Exception)
        {
            return false;
        }
    }

    private class CatalogInventoryData
    {
        public int TotalProducts { get; set; }
        public int LowStockCount { get; set; }
        public int OutOfStockProducts { get; set; }
        public decimal TotalInventoryValue { get; set; }
    }
}
