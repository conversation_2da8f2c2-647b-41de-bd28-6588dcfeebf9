# 🧪 GUIDE DE TEST MANUEL - SYSTÈME D'INVENTAIRE NAFAPLACE

## 🎯 **Objectif**
Tester toutes les fonctionnalités d'inventaire côté **Admin** et **Vendeur** dans des conditions réelles.

---

## 🔗 **LIENS IMPORTANTS**

### Portails Web
- **Admin Portal**: http://localhost:8081
- **Seller Portal**: http://localhost:8082
- **Site Principal**: http://localhost:8080

### APIs Backend
- **Inventory API Swagger**: http://localhost:5244/swagger
- **Catalog API Swagger**: http://localhost:5243/swagger
- **Notifications API**: http://localhost:5007

---

## 📋 **PHASE 1: TESTS CÔTÉ ADMIN PORTAL**

### ✅ **1.1 Accès et Navigation**
1. **Ouvrir**: http://localhost:8081
2. **Se connecter** avec un compte admin
3. **Naviguer** vers la section "Inventaire" ou "Gestion des Stocks"

### ✅ **1.2 Dashboard d'Inventaire Admin**
**À tester:**
- [ ] **Statistiques globales** affichées
  - Nombre total de produits
  - Produits en stock faible
  - Produits en rupture
  - Valeur totale de l'inventaire
- [ ] **Graphiques** et visualisations
- [ ] **Alertes récentes** listées
- [ ] **Produits les plus vendus**

### ✅ **1.3 Gestion des Alertes (Admin)**
**À tester:**
- [ ] **Liste des alertes** de tous les vendeurs
- [ ] **Filtrage** par type d'alerte (stock faible, rupture)
- [ ] **Filtrage** par vendeur
- [ ] **Marquer comme lu** une alerte
- [ ] **Actions** sur les alertes (résoudre, ignorer)

### ✅ **1.4 Supervision Globale**
**À tester:**
- [ ] **Vue d'ensemble** de tous les stocks
- [ ] **Recherche** de produits par nom/ID
- [ ] **Filtrage** par catégorie
- [ ] **Tri** par stock, valeur, vendeur
- [ ] **Export** des données (si disponible)

### ✅ **1.5 Tâches de Maintenance (Admin)**
**À tester:**
- [ ] **Statut** des tâches automatiques
- [ ] **Déclenchement manuel** des tâches
  - Nettoyage réservations expirées
  - Recalcul des stocks
  - Traitement des alertes
- [ ] **Logs** des tâches exécutées

---

## 📋 **PHASE 2: TESTS CÔTÉ SELLER PORTAL**

### ✅ **2.1 Accès Vendeur**
1. **Ouvrir**: http://localhost:8082
2. **Se connecter** avec un compte vendeur
3. **Naviguer** vers "Mon Inventaire" ou "Mes Stocks"

### ✅ **2.2 Dashboard Vendeur**
**À tester:**
- [ ] **Statistiques personnelles** du vendeur
  - Ses produits uniquement
  - Ses alertes uniquement
  - Valeur de son inventaire
- [ ] **Graphiques** de ses ventes
- [ ] **Alertes** de ses produits

### ✅ **2.3 Gestion des Stocks Vendeur**
**À tester:**
- [ ] **Liste** de ses produits uniquement
- [ ] **Modification** des quantités en stock
- [ ] **Ajout** de nouveaux produits
- [ ] **Seuils d'alerte** personnalisés
- [ ] **Historique** des mouvements de ses produits

### ✅ **2.4 Alertes Vendeur**
**À tester:**
- [ ] **Notifications** de stock faible
- [ ] **Notifications** de rupture
- [ ] **Actions** sur ses alertes
- [ ] **Paramétrage** des seuils d'alerte

---

## 📋 **PHASE 3: TESTS D'INTÉGRATION**

### ✅ **3.1 Notifications Automatiques**
**À tester:**
1. **Réduire** le stock d'un produit sous le seuil
2. **Vérifier** qu'une alerte est créée
3. **Vérifier** qu'une notification est envoyée
4. **Mettre** un produit en rupture (stock = 0)
5. **Vérifier** l'alerte de rupture

### ✅ **3.2 Synchronisation Catalog-Inventory**
**À tester:**
1. **Modifier** un stock dans l'inventaire
2. **Vérifier** la mise à jour dans le catalogue
3. **Créer** un nouveau produit
4. **Vérifier** l'initialisation du stock

### ✅ **3.3 Tâches de Maintenance**
**À tester:**
1. **Créer** une réservation temporaire
2. **Attendre** l'expiration (ou forcer)
3. **Vérifier** le nettoyage automatique
4. **Déclencher** un recalcul de stock
5. **Vérifier** la cohérence des données

---

## 📋 **PHASE 4: TESTS DE PERFORMANCE**

### ✅ **4.1 Charge de Données**
**À tester:**
- [ ] **Performance** avec beaucoup de produits
- [ ] **Temps de réponse** du dashboard
- [ ] **Pagination** des listes
- [ ] **Recherche** rapide

### ✅ **4.2 Concurrence**
**À tester:**
- [ ] **Plusieurs vendeurs** modifiant en même temps
- [ ] **Cohérence** des données
- [ ] **Gestion** des conflits

---

## 🎯 **CRITÈRES DE SUCCÈS**

### ✅ **Fonctionnalités Critiques**
- [ ] **Dashboard** affiche les bonnes données
- [ ] **Alertes** sont créées automatiquement
- [ ] **Notifications** sont envoyées
- [ ] **Séparation** Admin/Vendeur fonctionne
- [ ] **Synchronisation** Catalog-Inventory OK

### ✅ **Expérience Utilisateur**
- [ ] **Interface** intuitive et responsive
- [ ] **Navigation** fluide
- [ ] **Messages** d'erreur clairs
- [ ] **Performance** acceptable

### ✅ **Intégrité des Données**
- [ ] **Cohérence** entre services
- [ ] **Pas de perte** de données
- [ ] **Transactions** atomiques
- [ ] **Audit trail** (si implémenté)

---

## 🚨 **PROBLÈMES À SIGNALER**

### ❌ **Bugs Critiques**
- Données incorrectes
- Erreurs 500
- Perte de données
- Sécurité compromise

### ⚠️ **Améliorations**
- Performance lente
- UX confuse
- Fonctionnalités manquantes
- Messages peu clairs

---

## 📝 **RAPPORT DE TEST**

**Format suggéré:**
```
FONCTIONNALITÉ: [Nom]
STATUS: ✅ OK / ❌ KO / ⚠️ Partiel
DÉTAILS: [Description du test]
PROBLÈMES: [Si applicable]
SUGGESTIONS: [Si applicable]
```

---

## 🎉 **VALIDATION FINALE**

Le système d'inventaire est **COMPLET** si :
- ✅ Toutes les fonctionnalités Admin fonctionnent
- ✅ Toutes les fonctionnalités Vendeur fonctionnent  
- ✅ Les intégrations sont opérationnelles
- ✅ Les performances sont acceptables
- ✅ L'expérience utilisateur est satisfaisante
