# Script de test complet pour le système de coupons NafaPlace

Write-Host "=== TEST COMPLET DU SYSTÈME DE COUPONS NAFAPLACE ===" -ForegroundColor Green
Write-Host ""

# Configuration
$couponApiUrl = "http://localhost:5006"
$cartApiUrl = "http://localhost:5005"
$orderApiUrl = "http://localhost:5004"
$adminPortalUrl = "http://localhost:8081"
$webPortalUrl = "http://localhost:8080"

# Headers
$headers = @{
    "Content-Type" = "application/json"
    "Accept" = "application/json"
}

# Fonction de test simple
function Test-Endpoint {
    param([string]$Url, [string]$Name)
    
    Write-Host "Testing: $Name" -ForegroundColor Yellow
    try {
        $response = Invoke-RestMethod -Uri $Url -Headers $headers -TimeoutSec 10
        Write-Host "✅ SUCCESS" -ForegroundColor Green
        return @{ Success = $true; Data = $response }
    }
    catch {
        Write-Host "❌ FAILED: $($_.Exception.Message)" -ForegroundColor Red
        return @{ Success = $false; Error = $_.Exception.Message }
    }
}

function Test-WebPage {
    param([string]$Url, [string]$Name)
    
    Write-Host "Testing: $Name" -ForegroundColor Yellow
    try {
        $response = Invoke-WebRequest -Uri $Url -TimeoutSec 10 -UseBasicParsing
        Write-Host "✅ SUCCESS - HTTP $($response.StatusCode)" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "❌ FAILED: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

Write-Host "🎟️ PHASE 1: TESTS DES SERVICES BACKEND COUPONS" -ForegroundColor Magenta
Write-Host "=" * 60

# Test 1: Health Check du service Coupon
$test1 = Test-Endpoint -Url "$couponApiUrl/health" -Name "Coupon Service Health Check"

# Test 2: Liste des coupons disponibles
$test2 = Test-Endpoint -Url "$couponApiUrl/api/coupon" -Name "Liste des coupons disponibles"

# Test 3: Coupons actifs
$test3 = Test-Endpoint -Url "$couponApiUrl/api/coupon/active" -Name "Coupons actifs"

# Test 4: Statistiques des coupons
$test4 = Test-Endpoint -Url "$couponApiUrl/api/coupon/stats" -Name "Statistiques des coupons"

Write-Host ""
Write-Host "🛒 PHASE 2: TESTS D'INTÉGRATION PANIER-COUPONS" -ForegroundColor Magenta
Write-Host "=" * 60

# Test 5: Service Cart
$test5 = Test-Endpoint -Url "$cartApiUrl/health" -Name "Cart Service Health Check"

# Test 6: Service Order
$test6 = Test-Endpoint -Url "$orderApiUrl/health" -Name "Order Service Health Check"

Write-Host ""
Write-Host "🖥️ PHASE 3: TESTS DES PORTAILS WEB" -ForegroundColor Magenta
Write-Host "=" * 60

# Test 7: Admin Portal
$test7 = Test-WebPage -Url $adminPortalUrl -Name "Admin Portal Access"

# Test 8: Web Portal
$test8 = Test-WebPage -Url $webPortalUrl -Name "Web Portal Access"

Write-Host ""
Write-Host "📊 PHASE 4: ANALYSE DES DONNÉES DE COUPONS" -ForegroundColor Magenta
Write-Host "=" * 60

if ($test2.Success) {
    $coupons = $test2.Data
    Write-Host "COUPONS DISPONIBLES:" -ForegroundColor Cyan
    if ($coupons -is [array] -and $coupons.Count -gt 0) {
        foreach ($coupon in $coupons) {
            $status = if ($coupon.isActive) { "ACTIF" } else { "INACTIF" }
            $type = switch ($coupon.discountType) {
                1 { "Montant fixe" }
                2 { "Pourcentage" }
                3 { "Livraison gratuite" }
                4 { "Buy X Get Y" }
                default { "Autre" }
            }
            Write-Host "  - [$status] $($coupon.code): $type - $($coupon.description)" -ForegroundColor White
            if ($coupon.discountType -eq 1) {
                Write-Host "    Réduction: $($coupon.discountAmount) GNF" -ForegroundColor Green
            } elseif ($coupon.discountType -eq 2) {
                Write-Host "    Réduction: $($coupon.discountPercentage)%" -ForegroundColor Green
            }
            if ($coupon.minimumOrderAmount -gt 0) {
                Write-Host "    Commande minimum: $($coupon.minimumOrderAmount) GNF" -ForegroundColor Gray
            }
            Write-Host "    Utilisations: $($coupon.currentUsageCount)/$($coupon.maxUsageCount)" -ForegroundColor Gray
        }
    } else {
        Write-Host "  Aucun coupon trouvé ou format inattendu" -ForegroundColor Yellow
    }
}

if ($test4.Success) {
    Write-Host ""
    Write-Host "STATISTIQUES DES COUPONS:" -ForegroundColor Cyan
    $stats = $test4.Data
    Write-Host "  - Total coupons: $($stats.totalCoupons)" -ForegroundColor White
    Write-Host "  - Coupons actifs: $($stats.activeCoupons)" -ForegroundColor Green
    Write-Host "  - Coupons expirés: $($stats.expiredCoupons)" -ForegroundColor Red
    Write-Host "  - Utilisations totales: $($stats.totalUsages)" -ForegroundColor Blue
}

Write-Host ""
Write-Host "🧪 PHASE 5: TESTS FONCTIONNELS AVANCÉS" -ForegroundColor Magenta
Write-Host "=" * 60

# Test de validation d'un coupon spécifique
Write-Host "Testing: Validation du coupon WELCOME10" -ForegroundColor Yellow
try {
    $validationData = @{
        couponCode = "WELCOME10"
        cart = @{
            SubTotal = 75000
            Items = @(
                @{
                    ProductId = 1
                    Quantity = 2
                    UnitPrice = 37500
                    CategoryId = 1
                    SellerId = 1
                }
            )
            UserId = "test-user"
        }
    }
    
    $json = $validationData | ConvertTo-Json -Depth 10
    $content = [System.Text.Encoding]::UTF8.GetBytes($json)
    
    $response = Invoke-RestMethod -Uri "$couponApiUrl/api/coupon/validate" -Method POST -Body $content -ContentType "application/json" -TimeoutSec 10
    
    Write-Host "✅ SUCCESS - Validation coupon" -ForegroundColor Green
    Write-Host "  Coupon valide: $($response.success)" -ForegroundColor $(if ($response.success) { "Green" } else { "Red" })
    Write-Host "  Réduction: $($response.discountAmount) GNF" -ForegroundColor Green
    Write-Host "  Message: $($response.message)" -ForegroundColor Cyan
    $couponValidationSuccess = $true
}
catch {
    Write-Host "❌ FAILED: $($_.Exception.Message)" -ForegroundColor Red
    $couponValidationSuccess = $false
}

Write-Host ""
Write-Host "📋 RÉSUMÉ DES TESTS" -ForegroundColor Green
Write-Host "=" * 60

$totalTests = 9
$successCount = 0
if ($test1.Success) { $successCount++ }
if ($test2.Success) { $successCount++ }
if ($test3.Success) { $successCount++ }
if ($test4.Success) { $successCount++ }
if ($test5.Success) { $successCount++ }
if ($test6.Success) { $successCount++ }
if ($test7) { $successCount++ }
if ($test8) { $successCount++ }
if ($couponValidationSuccess) { $successCount++ }

Write-Host "Tests réussis: $successCount/$totalTests" -ForegroundColor $(if ($successCount -eq $totalTests) { "Green" } elseif ($successCount -gt 6) { "Yellow" } else { "Red" })

if ($successCount -eq $totalTests) {
    Write-Host "🎉 TOUS LES TESTS SONT PASSÉS!" -ForegroundColor Green
    Write-Host "Le système de coupons est PLEINEMENT OPÉRATIONNEL" -ForegroundColor Green
} elseif ($successCount -gt 6) {
    Write-Host "⚠️ LA PLUPART DES TESTS SONT RÉUSSIS" -ForegroundColor Yellow
    Write-Host "Le système de coupons est LARGEMENT FONCTIONNEL" -ForegroundColor Yellow
} else {
    Write-Host "❌ PLUSIEURS TESTS ONT ÉCHOUÉ" -ForegroundColor Red
    Write-Host "Vérifiez les services et la configuration" -ForegroundColor Red
}

Write-Host ""
Write-Host "🔗 LIENS POUR TESTS MANUELS:" -ForegroundColor Cyan
Write-Host "Admin Portal (Gestion coupons): $adminPortalUrl/coupons" -ForegroundColor White
Write-Host "Web Portal (Panier): $webPortalUrl/cart" -ForegroundColor White
Write-Host "Coupon API Swagger: $couponApiUrl/swagger" -ForegroundColor White

Write-Host ""
Write-Host "📝 PROCHAINES ÉTAPES DE TEST MANUEL:" -ForegroundColor Yellow
Write-Host "1. Aller sur le site web et ajouter des produits au panier" -ForegroundColor White
Write-Host "2. Appliquer un coupon (ex: WELCOME10, FREESHIP, SAVE50K)" -ForegroundColor White
Write-Host "3. Vérifier que la réduction est appliquée" -ForegroundColor White
Write-Host "4. Procéder au checkout et vérifier l'intégration" -ForegroundColor White
Write-Host "5. Vérifier dans l'admin portal que l'utilisation est enregistrée" -ForegroundColor White

Write-Host ""
Write-Host "Test terminé!" -ForegroundColor Green
