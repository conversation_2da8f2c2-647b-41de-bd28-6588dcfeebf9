# 🎟️ Guide de Test Complet - Système de Coupons NafaPlace

## 📋 Vue d'ensemble

Ce guide vous permet de tester toutes les fonctionnalités du système de coupons NafaPlace de manière complète et systématique.

## 🔗 URLs de Test

- **Site Web Principal** : http://localhost:8080
- **Admin Portal** : http://localhost:8081
- **Seller Portal** : http://localhost:8082
- **API Coupon (Swagger)** : http://localhost:5009/swagger
- **Test HTML** : file:///C:/Users/<USER>/Documents/nafaplace/nafaplace/test-coupon-manual.html

## 🧪 Phase 1: Tests Backend (APIs)

### 1.1 Test API Coupon
1. Ouvrir : http://localhost:5009/swagger
2. Tester les endpoints :
   - `GET /api/coupon` - Liste tous les coupons
   - `GET /api/coupon/active` - Coupons actifs
   - `GET /api/coupon/stats` - Statistiques
   - `POST /api/coupon/validate` - Validation d'un coupon

### 1.2 Coupons de Test Disponibles
Les coupons suivants devraient être disponibles :
- **WELCOME10** : 10% de réduction
- **SAVE50K** : 50,000 GNF de réduction fixe
- **FREESHIP** : Livraison gratuite
- **BINE100** : 20% de réduction (si créé)

## 🖥️ Phase 2: Tests Admin Portal

### 2.1 Gestion des Coupons
1. Aller sur : http://localhost:8081
2. Se connecter avec un compte admin
3. Naviguer vers : **Coupons** (menu latéral)
4. Vérifier :
   - ✅ Liste des coupons existants
   - ✅ Création d'un nouveau coupon
   - ✅ Modification d'un coupon
   - ✅ Activation/Désactivation
   - ✅ Statistiques d'utilisation

### 2.2 Créer un Coupon de Test
1. Cliquer sur **"Nouveau Coupon"**
2. Remplir :
   - **Code** : TEST2025
   - **Nom** : Coupon de Test 2025
   - **Type** : Pourcentage
   - **Valeur** : 15
   - **Commande minimum** : 50000 GNF
   - **Date de fin** : Dans 1 mois
3. Sauvegarder et vérifier qu'il apparaît dans la liste

## 🛒 Phase 3: Tests Site Web (Intégration Panier)

### 3.1 Préparation du Panier
1. Aller sur : http://localhost:8080
2. Se connecter ou continuer en tant qu'invité
3. Ajouter des produits au panier (minimum 75,000 GNF pour tester les seuils)
4. Aller au panier : http://localhost:8080/cart

### 3.2 Test d'Application de Coupon
1. Dans le panier, localiser la section **"Coupon de Réduction"**
2. Tester les coupons suivants :

#### Test WELCOME10 (10% de réduction)
- Entrer : `WELCOME10`
- Cliquer **"Appliquer"**
- **Résultat attendu** :
  - ✅ Message de succès
  - ✅ Réduction de 10% affichée
  - ✅ Total mis à jour
  - ✅ Bouton "Supprimer le coupon" visible

#### Test SAVE50K (50,000 GNF fixe)
- Supprimer le coupon précédent
- Entrer : `SAVE50K`
- Cliquer **"Appliquer"**
- **Résultat attendu** :
  - ✅ Réduction de 50,000 GNF
  - ✅ Total correctement calculé

#### Test FREESHIP (Livraison gratuite)
- Supprimer le coupon précédent
- Entrer : `FREESHIP`
- Cliquer **"Appliquer"**
- **Résultat attendu** :
  - ✅ Frais de livraison = 0 GNF
  - ✅ Message indiquant la livraison gratuite

### 3.3 Tests d'Erreur
1. **Coupon inexistant** :
   - Entrer : `FAUX_COUPON`
   - **Résultat attendu** : Message d'erreur "Coupon non trouvé"

2. **Coupon expiré** (si disponible) :
   - **Résultat attendu** : Message "Coupon expiré"

3. **Seuil minimum non atteint** :
   - Vider le panier, ajouter un produit < 50,000 GNF
   - Essayer `WELCOME10`
   - **Résultat attendu** : Message "Commande minimum non atteinte"

## 🛍️ Phase 4: Tests Processus de Commande

### 4.1 Checkout avec Coupon
1. Avec un coupon appliqué, cliquer **"Passer la commande"**
2. Aller à : http://localhost:8080/checkout
3. Vérifier :
   - ✅ Coupon affiché dans le résumé
   - ✅ Réduction correctement appliquée
   - ✅ Total final correct
   - ✅ TVA calculée sur le montant après réduction

### 4.2 Finalisation de Commande
1. Remplir les informations de livraison
2. Choisir un mode de paiement
3. Finaliser la commande
4. Vérifier :
   - ✅ Commande créée avec le coupon
   - ✅ Panier vidé après paiement
   - ✅ Utilisation du coupon enregistrée

## 📊 Phase 5: Vérification Post-Commande

### 5.1 Vérification Admin
1. Retourner sur l'Admin Portal
2. Aller dans **Coupons**
3. Vérifier :
   - ✅ Compteur d'utilisation incrémenté
   - ✅ Statistiques mises à jour

### 5.2 Vérification Commandes
1. Dans l'Admin Portal, aller dans **Commandes**
2. Trouver la commande récente
3. Vérifier :
   - ✅ Coupon appliqué visible
   - ✅ Montant de réduction correct
   - ✅ Total final correct

## ✅ Checklist de Validation Complète

### Backend (APIs)
- [ ] Service Coupon accessible (port 5009)
- [ ] Endpoints API fonctionnels
- [ ] Validation de coupons opérationnelle
- [ ] Statistiques disponibles

### Admin Portal
- [ ] Page de gestion des coupons accessible
- [ ] Création de coupons fonctionnelle
- [ ] Modification de coupons possible
- [ ] Statistiques d'utilisation visibles

### Site Web
- [ ] Composant coupon visible dans le panier
- [ ] Application de coupons fonctionnelle
- [ ] Messages d'erreur appropriés
- [ ] Calculs de réduction corrects

### Intégration Commandes
- [ ] Coupons pris en compte au checkout
- [ ] Enregistrement des utilisations
- [ ] Calculs finaux corrects
- [ ] Panier vidé après commande

## 🚨 Problèmes Courants et Solutions

### Problème : API non accessible
**Solution** : Vérifier que les conteneurs Docker sont démarrés
```bash
docker ps --filter "name=nafaplace"
```

### Problème : Coupons non visibles
**Solution** : Vérifier la base de données Coupon
```bash
docker logs nafaplace-coupon-api
```

### Problème : Erreurs CORS
**Solution** : Les APIs sont configurées pour accepter les requêtes cross-origin

### Problème : Calculs incorrects
**Solution** : Vérifier la logique de calcul dans le service Coupon

## 📈 Métriques de Succès

Un test complet réussi doit montrer :
- ✅ **100% des APIs** fonctionnelles
- ✅ **Toutes les interfaces** accessibles
- ✅ **Application de coupons** sans erreur
- ✅ **Intégration commandes** complète
- ✅ **Enregistrement des utilisations** correct

## 🎯 Prochaines Étapes

Après validation complète :
1. Tests de charge sur les APIs
2. Tests de sécurité (validation des entrées)
3. Tests de performance (temps de réponse)
4. Tests d'intégration avec d'autres services
5. Déploiement en environnement de test
